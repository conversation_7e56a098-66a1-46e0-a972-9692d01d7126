import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class LibraryScreen extends StatelessWidget {
  const LibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'مكتبتي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
          ),
          body: const Center(
            child: Text(
              'شاشة المكتبة\nقيد التطوير',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              // TODO: Add new book
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }
}
