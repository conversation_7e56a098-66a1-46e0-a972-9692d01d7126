import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../themes/app_themes.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _dayThemesExpanded = false;
  bool _nightThemesExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'ملفي الشخصي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileSection(theme),
                const SizedBox(height: 24),
                _buildThemesSection(themeProvider),
                const SizedBox(height: 24),
                _buildSettingsSection(theme),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileSection(theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: theme.progressGradient.first.withOpacity(0.2),
              child: Icon(
                Icons.person,
                size: 30,
                color: theme.progressGradient.first,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المستخدم',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: theme.primaryText,
                    ),
                  ),
                  Text(
                    'مرحباً بك في تطبيق إنجاز',
                    style: TextStyle(
                      color: theme.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                // TODO: Edit profile
              },
              icon: Icon(
                Icons.edit,
                color: theme.secondaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemesSection(ThemeProvider themeProvider) {
    final theme = themeProvider.currentTheme;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الهويات البصرية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            
            // Day Themes Section
            ExpansionTile(
              title: Text(
                'الهويات النهارية',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: theme.primaryText,
                ),
              ),
              initiallyExpanded: _dayThemesExpanded,
              onExpansionChanged: (expanded) {
                setState(() {
                  _dayThemesExpanded = expanded;
                });
              },
              children: [
                _buildThemeGrid(themeProvider.dayThemes, themeProvider),
              ],
            ),
            
            // Night Themes Section
            ExpansionTile(
              title: Text(
                'الهويات المسائية',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: theme.primaryText,
                ),
              ),
              initiallyExpanded: _nightThemesExpanded,
              onExpansionChanged: (expanded) {
                setState(() {
                  _nightThemesExpanded = expanded;
                });
              },
              children: [
                _buildThemeGrid(themeProvider.nightThemes, themeProvider),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeGrid(List<AppTheme> themes, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: themes.length,
        itemBuilder: (context, index) {
          final theme = themes[index];
          final isSelected = themeProvider.currentTheme.id == theme.id;
          
          return GestureDetector(
            onTap: () {
              themeProvider.setTheme(theme);
            },
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: theme.progressGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
                border: isSelected 
                  ? Border.all(color: Colors.white, width: 3)
                  : null,
              ),
              child: isSelected
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 20,
                  )
                : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildSettingsSection(theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.notifications, color: theme.secondaryText),
              title: Text(
                'الإشعارات',
                style: TextStyle(color: theme.primaryText),
              ),
              trailing: Icon(Icons.arrow_forward_ios, size: 16, color: theme.secondaryText),
              onTap: () {
                // TODO: Navigate to notifications settings
              },
            ),
            ListTile(
              leading: Icon(Icons.backup, color: theme.secondaryText),
              title: Text(
                'إدارة البيانات',
                style: TextStyle(color: theme.primaryText),
              ),
              trailing: Icon(Icons.arrow_forward_ios, size: 16, color: theme.secondaryText),
              onTap: () {
                // TODO: Navigate to data management
              },
            ),
          ],
        ),
      ),
    );
  }
}
